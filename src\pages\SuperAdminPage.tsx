import React, { useState } from 'react';
import SuperAdminSidebar from '../components/SuperAdminSidebar';
import SuperAdminDashboard from './SuperAdminDashboard';
import Organizations from './Organizations';
import OrganizationDetail from '../components/OrganizationDetail';

interface Organization {
  uuid: string;
  name: string;
  slug: string;
  description: string;
  logo_url: string | null;
  is_active: boolean;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

const SuperAdminPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);

  const handleSelectOrganization = (org: Organization) => {
    setSelectedOrganization(org);
  };

  const handleBackToOrganizations = () => {
    setSelectedOrganization(null);
  };

  const renderContent = () => {
    if (activeTab === 'organizations') {
      if (selectedOrganization) {
        return (
          <OrganizationDetail
            organization={selectedOrganization}
            onBack={handleBackToOrganizations}
          />
        );
      }
      return <Organizations onSelectOrganization={handleSelectOrganization} />;
    }
    
    return <SuperAdminDashboard />;
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <SuperAdminSidebar activeTab={activeTab} setActiveTab={setActiveTab} />
      <div className="flex-1">
        {renderContent()}
      </div>
    </div>
  );
};

export default SuperAdminPage;
