import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import SuperAdminSidebar from '../components/SuperAdminSidebar';
import SuperAdminDashboard from './SuperAdminDashboard';
import Organizations from './Organizations';
import OrganizationDetail from '../components/OrganizationDetail';

interface Organization {
  uuid: string;
  id?: string; // Alternative field name
  name: string;
  slug: string;
  description: string;
  logo_url: string | null;
  is_active: boolean;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

const SuperAdminPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(false);

  // Update active tab based on current route and fetch organization if needed
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/organizations')) {
      setActiveTab('organizations');

      // Check if we're viewing a specific organization
      const orgIdMatch = path.match(/\/organizations\/([^\/]+)/);
      if (orgIdMatch) {
        const orgId = orgIdMatch[1];
        fetchOrganization(orgId);
      } else {
        setSelectedOrganization(null);
      }
    } else if (path.includes('/dashboard')) {
      setActiveTab('dashboard');
      setSelectedOrganization(null);
    }
  }, [location.pathname]);

  const fetchOrganization = async (orgId: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('uuid', orgId)
        .single();

      if (error) throw error;
      setSelectedOrganization(data);
    } catch (err) {
      console.error('Error fetching organization:', err);
      // If organization not found, redirect back to organizations list
      navigate('/superadmin/organizations');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setSelectedOrganization(null); // Reset selected organization when changing tabs

    if (tab === 'dashboard') {
      navigate('/superadmin/dashboard');
    } else if (tab === 'organizations') {
      navigate('/superadmin/organizations');
    }
  };

  const handleSelectOrganization = (org: Organization) => {
    console.log('Selected organization:', org);
    console.log('Organization UUID:', org.uuid);
    console.log('Organization ID:', org.id);

    const orgId = org.uuid || org.id;
    console.log('Using organization ID:', orgId);

    if (!orgId) {
      console.error('No valid organization ID found!');
      return;
    }

    setSelectedOrganization(org);
    navigate(`/superadmin/organizations/${orgId}`);
  };

  const handleBackToOrganizations = () => {
    setSelectedOrganization(null);
    navigate('/superadmin/organizations');
  };

  const renderContent = () => {
    const path = location.pathname;

    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#9A00FF]"></div>
        </div>
      );
    }

    if (path.includes('/organizations')) {
      if (selectedOrganization) {
        return (
          <OrganizationDetail
            organization={selectedOrganization}
            onBack={handleBackToOrganizations}
          />
        );
      }
      return <Organizations onSelectOrganization={handleSelectOrganization} />;
    }

    return <SuperAdminDashboard />;
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <SuperAdminSidebar activeTab={activeTab} setActiveTab={handleTabChange} />
      <div className="flex-1">
        {renderContent()}
      </div>
    </div>
  );
};

export default SuperAdminPage;
